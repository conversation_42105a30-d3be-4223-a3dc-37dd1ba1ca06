import { Drawer } from 'expo-router/drawer';

import DrawerMenu from '@/components/ui/drawer-menu';
import styles from './styles';
import { useTranslation } from 'react-i18next';

const {drawerStyle} = styles;

export default function TabsLayout() {
  const {i18n} = useTranslation()
  return (
    <Drawer
      screenOptions={{
        headerShown: false,
        drawerType: 'front',
        drawerStyle: drawerStyle,
      }}
      key={i18n.language}
      drawerContent={(props) => <DrawerMenu {...props} />}
    >
      <Drawer.Screen name="index" />
      <Drawer.Screen name="settings" />
    </Drawer>
  );
}
