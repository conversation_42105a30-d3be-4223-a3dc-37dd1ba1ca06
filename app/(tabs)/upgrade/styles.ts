import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLOURS.white,
  },
  headerContainer: {
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  backButton: {
    marginBottom: 10,
  },
  headerTitle: {
    textAlign: 'center',
    marginBottom: 8,
  },
  headerSubtitle: {
    textAlign: 'center',
    marginHorizontal: 10,
    marginBottom: 15
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingHorizontal: 20,
    paddingBottom: 100, // Space for the fixed button
  },
  continueButton: {
    marginHorizontal: 20,
    backgroundColor: COLOURS.borderColor,
    borderRadius: 32,
    paddingVertical: 16,
    alignItems: 'center'
  },
  continueButtonText: {
    letterSpacing: 0.5,
  },
  bottomButtonView: {
    paddingVertical: 10,
    backgroundColor: COLOURS.white
  }
});

export default styles;
