import { router } from 'expo-router';

export interface PlanFeature {
  id: string;
  text: string;
}

export interface UpgradePlan {
  id: 'plus' | 'ultimate';
  name: string;
  price: string;
  icon: 'badge' | 'cup';
  features: PlanFeature[];
}

export const upgradePlans: UpgradePlan[] = [
  {
    id: 'plus',
    name: 'VetAssist Plus',
    price: '£6 /month',
    icon: 'badge',
    features: [
      {
        id: 'plus-1',
        text: 'Unlimited VetAssist AI chat',
      },
      {
        id: 'plus-2',
        text: 'Add pets just £2/month each',
      },
      {
        id: 'plus-3',
        text: 'Unlimited vet consultations',
      },
      {
        id: 'plus-4',
        text: 'Cancel anytime',
      },
    ],
  },
  {
    id: 'ultimate',
    name: 'VetAssist Ultimate',
    price: '£9 /month',
    icon: 'cup',
    features: [
      {
        id: 'ultimate-1',
        text: 'All of Plus, along with:',
      },
      {
        id: 'ultimate-2',
        text: 'Add pets just £3/month each',
      },
      {
        id: 'ultimate-3',
        text: 'Monthly parasite treatment delivered to your door!',
      },
    ],
  },
];

export const upgradeServices = {
  // Navigate back to previous screen
  goBack: () => {
    router.replace('/(tabs)/emergency-consultation');
  },

  // Handle plan selection
  handlePlanSelection: (planType: 'plus' | 'ultimate') => {
    console.log('Selected plan type:', planType);
    
    if (planType === 'plus') {
      // Handle VetAssist Plus plan
      console.log('Processing VetAssist Plus plan (£6/month)');
      // router.push('/(tabs)/payment-processing');
    } else if (planType === 'ultimate') {
      // Handle VetAssist Ultimate plan
      console.log('Processing VetAssist Ultimate plan (£9/month)');
      // router.push('/(tabs)/payment-processing');
    }
  },
};
