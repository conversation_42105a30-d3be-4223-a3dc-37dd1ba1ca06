import { useState } from 'react';
import { ScrollView, StatusBar, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StripeProvider } from '@stripe/stripe-react-native';

import { ArrowLeft } from '@/components/icons';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';

import { upgradeServices, upgradePlans } from './services';
import PlanCard from '@/components/ui/plan-card';
import PaymentSheet from '@/components/ui/payment-sheet';
import { paymentSheetServices } from '@/components/ui/payment-sheet/services';
import styles from './styles';

const {
  container,
  headerContainer,
  backButton,
  headerTitle,
  headerSubtitle,
  scrollContainer,
  scrollContentContainer,
  continueButton,
  continueButtonText,
  bottomButtonView,
} = styles;

export default function UpgradeScreen() {
  const [selectedPlan, setSelectedPlan] = useState<'plus' | 'ultimate' | null>(
    null
  );
  const [paymentSheetVisible, setPaymentSheetVisible] = useState(false);

  const handlePlanSelect = (plan: 'plus' | 'ultimate') => {
    setSelectedPlan(plan);
  };

  const handleContinue = () => {
    if (selectedPlan) {
      setPaymentSheetVisible(true);
    }
  };

  const handlePaymentSheetClose = () => {
    setPaymentSheetVisible(false);
  };

  return (
    <StripeProvider publishableKey={process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY || 'pk_test_placeholder'}>
      <SafeAreaView style={container}>
        <StatusBar barStyle='dark-content' backgroundColor={COLOURS.white} />

        {/* Header */}
        <View style={headerContainer}>
          <TouchableOpacity style={backButton} onPress={upgradeServices.goBack}>
            <ArrowLeft color={COLOURS.primary} />
          </TouchableOpacity>
        </View>

        <ScrollView
          style={scrollContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={scrollContentContainer}
        >
          <TextTypes type='h2' color={COLOURS.primary} customStyle={headerTitle}>
            VetAssist upgrade
          </TextTypes>
          <TextTypes
            type='body2'
            color={COLOURS.secondaryTint}
            customStyle={headerSubtitle}
          >
            With our upgrade plans, you can access 24/7 AI-powered veterinary
            assistance
          </TextTypes>
          {/* Render plans dynamically */}
          {upgradePlans.map((plan) => (
            <PlanCard
              key={plan.id}
              plan={plan}
              isSelected={selectedPlan === plan.id}
              onSelect={handlePlanSelect}
            />
          ))}
        </ScrollView>

        <View style={bottomButtonView}>
          {/* Continue Button - Fixed at bottom */}
          <TouchableOpacity
            style={[
              continueButton,
              selectedPlan && { backgroundColor: COLOURS.primary },
            ]}
            onPress={handleContinue}
            disabled={!selectedPlan}
          >
            <TextTypes
              type='h4'
              color={selectedPlan ? COLOURS.white : COLOURS.grayIcon}
              customStyle={continueButtonText}
            >
              CONTINUE TO PAYMENT
            </TextTypes>
          </TouchableOpacity>
        </View>
        {selectedPlan && (
          <PaymentSheet
            visible={paymentSheetVisible}
            onClose={handlePaymentSheetClose}
            planType={selectedPlan}
            planPrice={paymentSheetServices.formatPrice(selectedPlan)}
            userEmail="<EMAIL>"
          />
        )}
      </SafeAreaView>
    </StripeProvider>
  );
}
