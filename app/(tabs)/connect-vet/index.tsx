import React, { useEffect } from 'react';
import { View, StatusBar, TouchableOpacity } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import Loader from '@/components/ui/Loader';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';
import styles from './styles';
import { connectVetServices } from './services';
import { Cancel } from '@/components/icons';

const {
  container,
  closeButtonContainer,
  contentContainer,
  loaderContainer,
  mainHeading,
  subtitle,
} = styles;

export default function ConnectVetScreen() {

  const insects = useSafeAreaInsets();

  useEffect(() => {
    // Navigate to emergency consultation after 3 seconds
    const timer = setTimeout(() => {
      connectVetServices.navigateToEmergencyConsultation();
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <SafeAreaView style={container}>
      <StatusBar barStyle="dark-content" backgroundColor={COLOURS.white} />
      
      {/* Close button - top left */}
      <TouchableOpacity 
        style={[closeButtonContainer, {top: insects.top + 20}]}
        onPress={connectVetServices.goBack}
      >
        <Cancel color={COLOURS.primary}/>
      </TouchableOpacity>

      {/* Main content */}
      <View style={contentContainer}>
        {/* Loader */}
        <View style={loaderContainer}>
          <Loader />
        </View>

        {/* Main heading */}
        <TextTypes 
          type="h2" 
          color={COLOURS.primary} 
          customStyle={mainHeading}
        >
          Finding an available vet..
        </TextTypes>

        {/* Subtitle */}
        <TextTypes 
          type="body2" 
          color={COLOURS.secondaryTint} 
          customStyle={subtitle}
        >
          Connecting you and Arlo with an emergency veterinarian
        </TextTypes>
      </View>
    </SafeAreaView>
  );
} 