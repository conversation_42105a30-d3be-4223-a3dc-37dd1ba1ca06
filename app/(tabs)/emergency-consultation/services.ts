import { router } from 'expo-router';

export const emergencyConsultationServices = {
  // Navigate back to previous screen
  goBack: () => {
    router.back();
  },

  // Navigate to emergency consultation screen
  navigateToEmergencyConsultation: () => {
    router.push('/(tabs)/emergency-consultation');
  },

  // Handle payment selection
  handlePaymentSelection: (paymentType: 'session' | 'upgrade') => {
    console.log('Selected payment type:', paymentType);
    
    if (paymentType === 'session') {
      // Handle pay per session payment
      console.log('Processing £28 payment for session');
      // router.push('/(tabs)/payment-processing');
    } else if (paymentType === 'upgrade') {
      // Handle VetAssist upgrade
      console.log('Processing VetAssist upgrade');
      router.push('/(tabs)/upgrade');
    }
  },

  // Mock function to process payment
  processPayment: async (paymentType: 'session' | 'upgrade') => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ success: true, paymentType });
      }, 2000); // Simulate 2 second payment processing
    });
  },

  // Handle payment success
  handlePaymentSuccess: (paymentType: string) => {
    console.log('Payment successful for:', paymentType);
    // Navigate to video consultation screen
    // router.push('/(tabs)/video-consultation');
  },

  // Handle payment error
  handlePaymentError: (error: string) => {
    console.error('Payment failed:', error);
    // Show error message or retry
  },
}; 