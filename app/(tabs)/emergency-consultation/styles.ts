import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLOURS.white,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  closeButtonContainer: {
    zIndex: 1,
    marginTop: 20
  },
  headerContainer: {
    marginTop: 20,
    marginBottom: 20,
  },
  mainHeading: {
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    textAlign: 'center',
    marginHorizontal: 10
  },
  vetCard: {
    backgroundColor: COLOURS.white,
    borderRadius: 24,
    padding: 16,
    marginBottom: 30,
    shadowColor: COLOURS.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
    alignItems: 'center',
  },
  vetProfileImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 16,
  },
  vetName: {
    marginBottom: 8,
  },
  vetDescription: {
    textAlign: 'center',
    lineHeight: 21
  },
  paymentSection: {
    marginBottom: 20,
  },
  paymentTitle: {
    textAlign: 'center',
    marginBottom: 16
  },
  emergencyBanner: {
    backgroundColor: COLOURS.lightGreen40,
    borderRadius: 16,
    padding: 12,
    marginBottom: 20,
    flexDirection: 'row',
    gap: 5
  },
  emergencyBannerText: {
    lineHeight: 18,
    flex: 1,
  },
  rowView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 4,
  },
  paymentOption: {
    backgroundColor: COLOURS.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: COLOURS.borderColor,
  },
  paymentOptionSelected: {
    borderColor: COLOURS.primary,
    backgroundColor: COLOURS.primary,
  },
  paymentOptionSubtitle: {
    lineHeight: 18,
    marginTop: 7
  },
  continueButton: {
    backgroundColor: COLOURS.greyShade,
    borderRadius: 24,
    paddingVertical: 16,
    paddingHorizontal: 24,
    marginHorizontal: 20,
    marginBottom: 20,
    marginTop: 10,
    alignItems: 'center',
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
});

export default styles; 