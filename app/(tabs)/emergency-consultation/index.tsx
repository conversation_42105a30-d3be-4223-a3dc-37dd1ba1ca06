import { useState } from 'react';
import {
  Image,
  ScrollView,
  StatusBar,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';

import { Cancel, Info } from '@/components/icons';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';

import { emergencyConsultationServices } from './services';
import styles from './styles';

const {
  container,
  closeButtonContainer,
  scrollContainer,
  scrollContentContainer,
  headerContainer,
  mainHeading,
  subtitle,
  vetCard,
  vetProfileImage,
  vetName,
  vetDescription,
  paymentSection,
  paymentTitle,
  emergencyBanner,
  emergencyBannerText,
  paymentOption,
  paymentOptionSelected,
  paymentOptionSubtitle,
  continueButton,
  continueButtonText,
  rowView,
} = styles;

export default function EmergencyConsultationScreen() {
  const [selectedPayment, setSelectedPayment] = useState<
    'session' | 'upgrade' | null
  >(null);
  const insects = useSafeAreaInsets();

  const handlePaymentSelect = (type: 'session' | 'upgrade') => {
    setSelectedPayment(type);
  };

  const handleContinue = () => {
    if (selectedPayment) {
      emergencyConsultationServices.handlePaymentSelection(selectedPayment);
    }
  };

  return (
    <SafeAreaView style={container}>
      <StatusBar barStyle='dark-content' backgroundColor={COLOURS.white} />

      <ScrollView
        style={scrollContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={scrollContentContainer}
      >
        {/* Close button - top left */}
        <TouchableOpacity
          style={closeButtonContainer}
          onPress={emergencyConsultationServices.goBack}
        >
          <Cancel color={COLOURS.primary} />
        </TouchableOpacity>
        {/* Header */}
        <View style={headerContainer}>
          <TextTypes
            type='h2'
            color={COLOURS.primary}
            customStyle={mainHeading}
          >
            Emergency vet consultation
          </TextTypes>
          <TextTypes
            type='body2'
            color={COLOURS.secondaryTint}
            customStyle={subtitle}
          >
            Get immediate help for Arlo's chocolate toxicity
          </TextTypes>
        </View>

        {/* Vet Profile Card */}
        <View style={vetCard}>
          <Image
            source={require('@/assets/images/icon.png')}
            style={vetProfileImage}
            defaultSource={require('@/assets/images/icon.png')}
          />
          <TextTypes type='h2' color={COLOURS.primary} customStyle={vetName}>
            Dr. Sarah T.
          </TextTypes>
          <TextTypes
            type='h5'
            color={COLOURS.secondaryTint}
            customStyle={vetDescription}
          >
            Emergency veterinarian specialist is Available now fo video
            consultation.
          </TextTypes>
        </View>

        {/* Payment Section */}
        <View style={paymentSection}>
          <TextTypes
            type='h5'
            color={COLOURS.primary}
            customStyle={paymentTitle}
          >
            Please select payment option
          </TextTypes>

          {/* Emergency Banner */}
          <View style={emergencyBanner}>
            <Info width={18} height={18} color={COLOURS.primary} />
            <TextTypes
              type='small'
              color={COLOURS.primary}
              customStyle={emergencyBannerText}
            >
              This is an emergency - connecting you immediately after payment
            </TextTypes>
          </View>

          {/* Payment Options */}
          <TouchableOpacity
            style={[
              paymentOption,
              selectedPayment === 'session' && paymentOptionSelected,
            ]}
            onPress={() => handlePaymentSelect('session')}
          >
            <View style={rowView}>
              <TextTypes
                type='h4'
                color={
                  selectedPayment === 'session'
                    ? COLOURS.white
                    : COLOURS.primary
                }
              >
                Pay Per Session
              </TextTypes>
              <TextTypes
                type='h4'
                color={
                  selectedPayment === 'session'
                    ? COLOURS.white
                    : COLOURS.primary
                }
              >
                £28
              </TextTypes>
            </View>
            <TextTypes
              type='label2'
              color={
                selectedPayment === 'session'
                  ? COLOURS.white
                  : COLOURS.secondaryTint
              }
              customStyle={paymentOptionSubtitle}
            >
              One-time payment for this consultation
            </TextTypes>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              paymentOption,
              selectedPayment === 'upgrade' && paymentOptionSelected,
            ]}
            onPress={() => handlePaymentSelect('upgrade')}
          >
            <View style={rowView}>
              <TextTypes
                type='h4'
                color={
                  selectedPayment === 'upgrade'
                    ? COLOURS.white
                    : COLOURS.primary
                }
              >
                VetAssist Upgrade
              </TextTypes>
              <TextTypes
                type='h4'
                color={
                  selectedPayment === 'upgrade'
                    ? COLOURS.white
                    : COLOURS.primary
                }
              >
                FREE
              </TextTypes>
            </View>
            <TextTypes
              type='label2'
              color={
                selectedPayment === 'upgrade'
                  ? COLOURS.white
                  : COLOURS.secondaryTint
              }
              customStyle={paymentOptionSubtitle}
            >
              Unlimited consultations + premium features
            </TextTypes>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Continue Button - Fixed at bottom */}
      <TouchableOpacity
        style={[
          continueButton,
          selectedPayment && { backgroundColor: COLOURS.primary },
        ]}
        onPress={handleContinue}
        disabled={!selectedPayment}
      >
        <TextTypes
          type='h4'
          color={COLOURS.white}
          customStyle={continueButtonText}
        >
          SELECT & CONTINUE
        </TextTypes>
      </TouchableOpacity>
    </SafeAreaView>
  );
}
