import {
  TouchableOpacity,
  TouchableOpacityProps,
  ViewStyle,
  StyleProp,
  TextStyle
} from 'react-native';

import TextTypes from '@/components/text-types';

import { buttonStyles, getTextColor } from './service';

interface ButtonProps extends TouchableOpacityProps {
  variant?:
    | 'primary'
    | 'secondary'
    | 'outline'
    | 'tertiary'
    | 'disabled'
    | 'small'
    | 'smallSecondary'
    | 'smallOutline'
    | 'smallTertiary';
  children: string;
  customStyle?: ViewStyle;
  textColor?: string;
  btnTextStyle?: StyleProp<TextStyle>
}

const Button = ({
  variant = 'primary',
  children,
  customStyle,
  textColor,
  disabled,
  btnTextStyle,
  ...props
}: ButtonProps) => {
  const buttonVariant = disabled ? 'disabled' : variant;
  const styles = buttonStyles(buttonVariant);
  const textColorValue = getTextColor(buttonVariant, textColor);

  return (
    <TouchableOpacity
      {...props}
      disabled={disabled}
      style={[...styles, customStyle]}
      activeOpacity={0.8}
    >
      <TextTypes type='h4' color={textColorValue} customStyle={btnTextStyle}>
        {children}
      </TextTypes>
    </TouchableOpacity>
  );
};

export default Button;
