import { StyleSheet } from 'react-native';

import { FONTS } from '@/constants/fonts';

const styles = () =>
  StyleSheet.create({
    h1: {
      fontFamily: FONTS.bold,
      fontSize: 26,
    },
    h2: {
      fontFamily: FONTS.bold,
      fontSize: 22,
    },
    h3: {
      fontFamily: FONTS.bold,
      fontSize: 20,
    },
    h4: {
      fontFamily: FONTS.bold,
      fontSize: 16,
    },
    h5: {
      fontFamily: FONTS.bold,
      fontSize: 14,
    },
    body1: {
      fontFamily: FONTS.regular,
      fontSize: 16,
    },
    body2: {
      fontFamily: FONTS.semiBold,
      fontSize: 14,
      lineHeight: 21
    },
    body3: {
      fontFamily: FONTS.regular,
      fontSize: 14,
      lineHeight: 21
    },
    small: {
      fontFamily: FONTS.regular,
      fontSize: 12,
    },
    label: {
      fontFamily: FONTS.regular,
      fontSize: 12,
    },
    label2: {
      fontFamily: FONTS.semiBold,
      fontSize: 12,
    },
    buttonText: {
      fontFamily: FONTS.regular,
      fontSize: 16,
      lineHeight: 24
    },
    navBar: {
      fontFamily: FONTS.bold,
      fontSize: 11,
    },
    errorText: {
      fontFamily: FONTS.bold,
      fontSize: 12,
    },
  });

export default styles;
