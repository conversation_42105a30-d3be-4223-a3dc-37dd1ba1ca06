import * as React from 'react';
import Svg, { G, <PERSON>, Defs, ClipPath, Rect } from 'react-native-svg';
import { COLOURS } from '@/constants/colours';

const SvgAmericanExpress = ({ width = 24, height = 16, ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 16'
    fill='none'
    {...props}
  >
    <Defs>
      <ClipPath id='clip0_154512_9913'>
        <Rect width='24' height='16' fill={COLOURS.white} />
      </ClipPath>
    </Defs>
    <G clipPath='url(#clip0_154512_9913)'>
      <Path
        d='M22 0H2C0.89543 0 0 0.89543 0 2V14C0 15.1046 0.89543 16 2 16H22C23.1046 16 24 15.1046 24 14V2C24 0.89543 23.1046 0 22 0Z'
        fill={COLOURS.amexBlue}
      />
      <Path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M13.7637 13.3939V7.6925L23.9112 7.70161V9.27651L22.7383 10.5299L23.9112 11.7948V13.4031H22.0386L21.0434 12.3049L20.0553 13.4072L13.7637 13.3939Z'
        fill='#FFFFFE'
      />
      <Path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M14.4414 12.7688V8.31995H18.2137V9.34483H15.6628V10.0405H18.1529V11.0483H15.6628V11.7317H18.2137V12.7688H14.4414Z'
        fill={COLOURS.amexBlue}
      />
      <Path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M18.1954 12.7687L20.2827 10.5417L18.1953 8.31995H19.811L21.0865 9.72998L22.3656 8.31995H23.9117V8.35495L21.8689 10.5417L23.9117 12.7056V12.7687H22.35L21.0519 11.3446L19.7671 12.7687H18.1954Z'
        fill={COLOURS.amexBlue}
      />
      <Path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M14.2369 2.63196H16.6829L17.5421 4.58281V2.63196H20.5619L21.0827 4.09353L21.6052 2.63196H23.9111V8.33335H11.7246L14.2369 2.63196Z'
        fill='#FFFFFE'
      />
      <Path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M14.7006 3.25134L12.7266 7.69651H14.0805L14.4529 6.80635H16.4708L16.843 7.69651H18.2306L16.2648 3.25134H14.7006ZM14.8702 5.80878L15.4622 4.39371L16.0538 5.80878H14.8702Z'
        fill={COLOURS.amexBlue}
      />
      <Path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M18.2119 7.69569V3.25061L20.115 3.25715L21.0943 5.98988L22.0799 3.25061H23.9115V7.69569L22.7329 7.70612V4.65278L21.6204 7.69569H20.5446L19.4089 4.64235V7.69569H18.2119Z'
        fill={COLOURS.amexBlue}
      />
    </G>
  </Svg>
);

export default SvgAmericanExpress;
