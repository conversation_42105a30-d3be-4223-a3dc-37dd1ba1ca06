import * as React from 'react';
import Svg, { G, <PERSON>, Defs, ClipPath, Rect } from 'react-native-svg';
import { COLOURS } from '@/constants/colours';

const SvgDiscover = ({ width = 24, height = 16, ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 16'
    fill='none'
    {...props}
  >
    <Defs>
      <ClipPath id='clip0_154512_9914'>
        <Rect width='24' height='16' fill={COLOURS.white} />
      </ClipPath>
    </Defs>
    <G clipPath='url(#clip0_154512_9914)'>
      <Path
        d='M21.9972 15.7499L21.9994 15.7499C22.9545 15.7581 23.7381 14.9773 23.75 14.0042L23.75 2.0063C23.7462 1.53569 23.5589 1.08617 23.2297 0.756802C22.9014 0.428269 22.4589 0.246149 21.9972 0.250071L2.00064 0.250062C1.54109 0.246149 1.09858 0.428269 0.770279 0.756802C0.441145 1.08617 0.253838 1.53569 0.250008 2.00426L0.25 13.9937C0.253838 14.4643 0.441145 14.9138 0.770279 15.2432C1.09858 15.5717 1.54109 15.7538 2.00277 15.7499H21.9972ZM21.9962 16.2499C21.9958 16.2499 21.9955 16.2499 21.9951 16.2499L21.9972 16.2499H21.9962Z'
        fill={COLOURS.white}
        stroke={COLOURS.black}
        strokeOpacity='0.2'
        strokeWidth='0.5'
      />
      <Path
        d='M12.6123 15.9999H21.9971C22.5239 16.0043 23.0309 15.7993 23.4065 15.4299C23.7821 15.0605 23.9955 14.557 23.9999 14.0303V11.6716C20.4561 13.7059 16.6127 15.1668 12.6123 15.9999Z'
        fill={COLOURS.discoverOrange}
      />
      <Path
        d='M23.172 9.29643H22.3196L21.3596 8.03023H21.2685V9.29643H20.5734V6.15161H21.5996C22.4023 6.15161 22.8658 6.48264 22.8658 7.0785C22.8658 7.56678 22.5761 7.88126 22.0547 7.98057L23.172 9.29643ZM22.1458 7.10333C22.1458 6.79712 21.914 6.63988 21.4837 6.63988H21.2685V7.59161H21.4671C21.914 7.59161 22.1458 7.42609 22.1458 7.10333ZM18.1403 6.15161H20.1099V6.68126H18.8354V7.38471H20.0603V7.92264H18.8354V8.77505H20.1099V9.30471H18.1403V6.15161ZM15.9058 9.37919L14.3996 6.14333H15.1609L16.1127 8.26195L17.0727 6.14333H17.8175L16.2947 9.37919H15.9223H15.9058ZM9.60784 9.37092C8.54853 9.37092 7.72094 8.65092 7.72094 7.71574C7.72094 6.8054 8.56508 6.06885 9.62439 6.06885C9.92232 6.06885 10.1706 6.12678 10.4768 6.25919V6.98747C10.2449 6.75965 9.93291 6.63187 9.60784 6.63161C8.94577 6.63161 8.44094 7.11161 8.44094 7.71574C8.44094 8.35299 8.93749 8.80816 9.64094 8.80816C9.95542 8.80816 10.1954 8.70885 10.4768 8.46057V9.18885C10.1623 9.32126 9.89749 9.37092 9.60784 9.37092ZM7.50577 8.33643C7.50577 8.94885 7.00094 9.37092 6.27267 9.37092C5.74301 9.37092 5.36232 9.18885 5.03956 8.77505L5.49473 8.38609C5.65198 8.66747 5.9168 8.80816 6.24784 8.80816C6.56232 8.80816 6.78577 8.61781 6.78577 8.36954C6.78577 8.22885 6.71956 8.12126 6.57887 8.0385C6.42461 7.96365 6.26397 7.90271 6.09887 7.85643C5.44508 7.64954 5.22163 7.42609 5.22163 6.98747C5.22163 6.47436 5.70163 6.0854 6.3306 6.0854C6.72784 6.0854 7.0837 6.20954 7.38163 6.44126L7.01749 6.85505C6.87317 6.69683 6.66889 6.60671 6.45473 6.60678C6.1568 6.60678 5.94163 6.75574 5.94163 6.95436C5.94163 7.11988 6.06577 7.21092 6.47956 7.35161C7.27404 7.59988 7.50577 7.83161 7.50577 8.34471V8.33643ZM4.08784 6.15161H4.78301V9.30471H4.08784V6.15161ZM1.85336 9.30471H0.827148V6.15161H1.85336C2.97887 6.15161 3.7568 6.79712 3.7568 7.72402C3.7568 8.19574 3.52508 8.64264 3.11956 8.94057C2.77198 9.18885 2.38301 9.30471 1.84508 9.30471H1.85336ZM2.66439 6.93781C2.43267 6.75574 2.16784 6.68954 1.71267 6.68954H1.52232V8.77505H1.71267C2.15956 8.77505 2.44094 8.6923 2.66439 8.52678C2.90439 8.32816 3.04508 8.03023 3.04508 7.72402C3.04508 7.41781 2.90439 7.12816 2.66439 6.93781Z'
        fill={COLOURS.black}
      />
      <Path
        d='M12.414 6.06885C11.5036 6.06885 10.7588 6.79712 10.7588 7.69919C10.7588 8.65919 11.4705 9.37919 12.414 9.37919C13.3409 9.37919 14.0691 8.65092 14.0691 7.72402C14.0691 6.79712 13.3491 6.06885 12.414 6.06885Z'
        fill={COLOURS.discoverOrange}
      />
    </G>
  </Svg>
);

export default SvgDiscover;
