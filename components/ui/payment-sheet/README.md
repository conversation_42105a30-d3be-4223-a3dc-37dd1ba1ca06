# Payment Sheet Component

A React Native component that provides a bottom sheet interface for processing payments using Stripe.

## Features

- Bottom sheet modal with smooth animations
- Stripe card input integration
- Form validation
- Loading states during payment processing
- Responsive design matching the app's design system

## Usage

```tsx
import PaymentSheet from '@/components/ui/payment-sheet';

function YourComponent() {
  const [paymentSheetVisible, setPaymentSheetVisible] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<'plus' | 'ultimate'>('plus');

  return (
    <PaymentSheet
      visible={paymentSheetVisible}
      onClose={() => setPaymentSheetVisible(false)}
      planType={selectedPlan}
      planPrice="£9.00 /month"
      userEmail="<EMAIL>"
    />
  );
}
```

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `visible` | `boolean` | Yes | Controls the visibility of the payment sheet |
| `onClose` | `() => void` | Yes | Callback function called when the sheet is closed |
| `planType` | `'plus' \| 'ultimate'` | Yes | The type of plan being purchased |
| `planPrice` | `string` | Yes | The formatted price string to display |
| `userEmail` | `string` | No | Pre-filled email address (optional) |

## Setup Requirements

### 1. Install Stripe React Native

```bash
npm install @stripe/stripe-react-native
```

### 2. Configure Stripe in app.json

Add the Stripe plugin to your `app.json`:

```json
{
  "expo": {
    "plugins": [
      [
        "@stripe/stripe-react-native",
        {
          "merchantIdentifier": "merchant.com.vetassist.app",
          "enableGooglePay": true
        }
      ]
    ]
  }
}
```

### 3. Environment Variables

Add your Stripe publishable key to your environment variables:

```env
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_key_here
```

### 4. Wrap with StripeProvider

Wrap your component tree with the StripeProvider:

```tsx
import { StripeProvider } from '@stripe/stripe-react-native';

function App() {
  return (
    <StripeProvider publishableKey={process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY}>
      {/* Your app components */}
    </StripeProvider>
  );
}
```

## Backend Integration

The component currently uses mock payment processing. To integrate with a real backend:

1. Update `services.ts` to call your backend API
2. Implement payment intent creation on your server
3. Handle payment confirmation with Stripe

Example backend integration:

```typescript
// In services.ts
processPayment: async (paymentData: PaymentData): Promise<PaymentResult> => {
  try {
    // Create payment intent on your backend
    const response = await fetch('/api/create-payment-intent', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        amount: getPlanAmount(paymentData.planType),
        currency: 'gbp',
        customer_email: paymentData.email,
      }),
    });
    
    const { client_secret } = await response.json();
    
    // Confirm payment with Stripe
    const { error } = await confirmPayment(client_secret, {
      paymentMethodType: 'Card',
      paymentMethodData: {
        billingDetails: {
          name: paymentData.cardholderName,
          email: paymentData.email,
        },
      },
    });
    
    if (error) {
      return { success: false, error: error.message };
    }
    
    return { success: true };
  } catch (error) {
    return { success: false, error: 'Payment failed' };
  }
}
```

## Styling

The component uses the app's design system colors and follows the established UI patterns. Styles can be customized in `styles.ts`.

## Testing

For testing purposes, the component includes mock payment processing with a 90% success rate. This can be adjusted in the `services.ts` file.
