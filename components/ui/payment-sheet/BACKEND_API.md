# Backend API Requirements for Payment Sheet

This document outlines the backend API endpoints needed to support the payment sheet functionality with Stripe integration.

## Required Backend Endpoints

### 1. Create Payment Intent

**Endpoint:** `POST /api/v1/payments/create-intent`

**Purpose:** Creates a Stripe payment intent for the subscription payment.

**Headers:**
```
Content-Type: application/json
Authorization: Bearer {access_token}
```

**Request Body:**
```json
{
  "amount": 900,
  "currency": "gbp",
  "customer_email": "<EMAIL>",
  "plan_type": "ultimate",
  "metadata": {
    "cardholder_name": "John Do<PERSON>",
    "country": "United Kingdom"
  }
}
```

**Response:**
```json
{
  "client_secret": "pi_1234567890_secret_abcdef",
  "payment_intent_id": "pi_1234567890",
  "amount": 900,
  "currency": "gbp"
}
```

**Backend Implementation Example (Node.js/Express):**
```javascript
app.post('/api/v1/payments/create-intent', async (req, res) => {
  try {
    const { amount, currency, customer_email, plan_type, metadata } = req.body;
    
    // Create payment intent with Stripe
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency,
      customer_email,
      metadata: {
        plan_type,
        ...metadata
      },
      automatic_payment_methods: {
        enabled: true,
      },
    });
    
    res.json({
      client_secret: paymentIntent.client_secret,
      payment_intent_id: paymentIntent.id,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});
```

### 2. Activate Subscription

**Endpoint:** `POST /api/v1/subscriptions/activate`

**Purpose:** Activates the user's subscription after successful payment.

**Headers:**
```
Content-Type: application/json
Authorization: Bearer {access_token}
```

**Request Body:**
```json
{
  "payment_intent_id": "pi_1234567890",
  "plan_type": "ultimate"
}
```

**Response:**
```json
{
  "success": true,
  "subscription": {
    "id": "sub_1234567890",
    "plan_type": "ultimate",
    "status": "active",
    "current_period_start": "2024-01-01T00:00:00Z",
    "current_period_end": "2024-02-01T00:00:00Z"
  }
}
```

**Backend Implementation Example:**
```javascript
app.post('/api/v1/subscriptions/activate', async (req, res) => {
  try {
    const { payment_intent_id, plan_type } = req.body;
    const userId = req.user.id; // From auth middleware
    
    // Verify payment intent was successful
    const paymentIntent = await stripe.paymentIntents.retrieve(payment_intent_id);
    
    if (paymentIntent.status !== 'succeeded') {
      return res.status(400).json({ error: 'Payment not completed' });
    }
    
    // Create or update subscription in your database
    const subscription = await createUserSubscription({
      userId,
      planType: plan_type,
      stripePaymentIntentId: payment_intent_id,
      status: 'active',
    });
    
    res.json({
      success: true,
      subscription,
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});
```

### 3. Webhook Handler (Recommended)

**Endpoint:** `POST /api/v1/webhooks/stripe`

**Purpose:** Handle Stripe webhook events for payment status updates.

**Implementation Example:**
```javascript
app.post('/api/v1/webhooks/stripe', express.raw({type: 'application/json'}), (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET);
  } catch (err) {
    return res.status(400).send(`Webhook signature verification failed.`);
  }

  // Handle the event
  switch (event.type) {
    case 'payment_intent.succeeded':
      const paymentIntent = event.data.object;
      // Update subscription status in database
      break;
    case 'payment_intent.payment_failed':
      const failedPayment = event.data.object;
      // Handle failed payment
      break;
    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  res.json({received: true});
});
```

## Environment Variables Needed

```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Database
DATABASE_URL=your_database_connection_string
```

## Database Schema Example

```sql
-- Subscriptions table
CREATE TABLE subscriptions (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL,
  plan_type VARCHAR(20) NOT NULL, -- 'plus' or 'ultimate'
  status VARCHAR(20) NOT NULL, -- 'active', 'cancelled', 'past_due'
  stripe_payment_intent_id VARCHAR(255),
  current_period_start TIMESTAMP,
  current_period_end TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Payment history table
CREATE TABLE payment_history (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL,
  subscription_id INTEGER,
  stripe_payment_intent_id VARCHAR(255) NOT NULL,
  amount INTEGER NOT NULL, -- Amount in pence
  currency VARCHAR(3) DEFAULT 'gbp',
  status VARCHAR(20) NOT NULL, -- 'succeeded', 'failed', 'pending'
  created_at TIMESTAMP DEFAULT NOW()
);
```

## Security Considerations

1. **Always validate payment status** on the backend before activating subscriptions
2. **Use webhook events** to handle payment status changes reliably
3. **Store sensitive data securely** and never expose Stripe secret keys to the frontend
4. **Implement proper authentication** for all payment-related endpoints
5. **Log all payment activities** for audit purposes
6. **Handle edge cases** like partial payments, refunds, and disputes

## Testing

Use Stripe's test card numbers for testing:
- **Successful payment:** ****************
- **Declined payment:** ****************
- **Requires authentication:** ****************

## Integration Steps

1. Set up the backend endpoints as shown above
2. Configure Stripe webhooks in your Stripe dashboard
3. Update the frontend services to call your real API endpoints
4. Test the complete payment flow with Stripe test cards
5. Deploy and configure production Stripe keys
