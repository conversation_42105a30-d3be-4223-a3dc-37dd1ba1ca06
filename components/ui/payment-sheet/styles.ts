import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: COLOURS.modalBackground,
    justifyContent: 'flex-end',
  },
  sheet: {
    backgroundColor: COLOURS.white,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 16,
    paddingHorizontal: 24,
    paddingBottom: 32,
    maxHeight: '90%',
    minHeight: '90%',
    shadowColor: COLOURS.textBlack,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 10,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  closeButton: {
    padding: 8,
    marginRight: 8,
  },
  logo: {
    width: 120,
    height: 40,
    marginBottom: 16,
  },
  planTitle: {
    textAlign: 'center',
    marginBottom: 8,
  },
  planPriceText: {
    textAlign: 'center',
    marginBottom: 16,
    fontWeight: 'bold',
  },
  renewalText: {
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 12,
    marginTop: 24,
    fontWeight: '600',
  },
  emailInput: {
    borderWidth: 1,
    borderColor: COLOURS.greyMedium,
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    color: COLOURS.textBlack,
    backgroundColor: COLOURS.white,
    marginBottom: 16,
  },
  cardFieldContainer: {
    borderWidth: 1,
    borderColor: COLOURS.greyMedium,
    borderRadius: 8,
    backgroundColor: COLOURS.white,
    marginBottom: 8,
    minHeight: 56,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingRight: 16,
  },
  cardNumberContainer: {
    borderWidth: 1,
    borderColor: COLOURS.greyMedium,
    borderRadius: 8,
    backgroundColor: COLOURS.white,
    marginBottom: 8,
    minHeight: 56,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingRight: 16,
  },
  cardNumberInput: {
    flex: 1,
    paddingHorizontal: 16,
    fontSize: 16,
    color: COLOURS.textBlack,
    height: 56,
  },
  cardRowContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 8,
  },
  cardExpiryContainer: {
    flex: 1,
    borderWidth: 1,
    borderColor: COLOURS.greyMedium,
    borderRadius: 8,
    backgroundColor: COLOURS.white,
    minHeight: 56,
    justifyContent: 'center',
  },
  cardExpiryInput: {
    paddingHorizontal: 16,
    fontSize: 16,
    color: COLOURS.textBlack,
    height: 56,
  },
  cardCvcContainer: {
    flex: 1,
    borderWidth: 1,
    borderColor: COLOURS.greyMedium,
    borderRadius: 8,
    backgroundColor: COLOURS.white,
    minHeight: 56,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingRight: 16,
  },
  cardCvcInput: {
    paddingHorizontal: 16,
    fontSize: 16,
    color: COLOURS.textBlack,
    flex: 1,
    height: 56,
  },
  cvcIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: COLOURS.greyMedium,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardholderInput: {
    borderWidth: 1,
    borderColor: COLOURS.greyMedium,
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    color: COLOURS.textBlack,
    backgroundColor: COLOURS.white,
  },
  countryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: COLOURS.greyMedium,
    borderRadius: 8,
    padding: 16,
    backgroundColor: COLOURS.white,
    marginBottom: 32,
  },
  countryText: {
    fontSize: 16,
    color: COLOURS.textBlack,
  },
  chevronDown: {
    width: 0,
    height: 0,
    borderLeftWidth: 6,
    borderRightWidth: 6,
    borderTopWidth: 8,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: COLOURS.secondaryTint,
  },
  confirmButton: {
    backgroundColor: COLOURS.primary,
    borderRadius: 24,
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
    marginTop: 16,
  },
  confirmButtonDisabled: {
    backgroundColor: COLOURS.grayIcon,
  },
  confirmButtonText: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  confirmButtonTextDisabled: {
    color: COLOURS.white,
  },
});

export default styles;
