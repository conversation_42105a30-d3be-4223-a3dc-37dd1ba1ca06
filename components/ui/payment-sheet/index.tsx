import React, { useState } from 'react';
import {
  View,
  Modal,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
} from 'react-native';
import { CardField, useStripe } from '@stripe/stripe-react-native';

import TextTypes from '@/components/text-types';
import { Close, VetAssistIcon } from '@/components/icons';
import { COLOURS } from '@/constants/colours';
import { paymentSheetServices } from './services';
import styles from './styles';

interface PaymentSheetProps {
  visible: boolean;
  onClose: () => void;
  planType: 'plus' | 'ultimate';
  planPrice: string;
  userEmail?: string;
}

const PaymentSheet: React.FC<PaymentSheetProps> = ({
  visible,
  onClose,
  planType,
  planPrice,
  userEmail = '',
}) => {
  const { confirmPayment, createPaymentMethod } = useStripe();
  const [email, setEmail] = useState(userEmail);
  const [cardholderName, setCardholderName] = useState('');
  const [country, setCountry] = useState('United Kingdom');
  const [isProcessing, setIsProcessing] = useState(false);
  const [cardNumber, setCardNumber] = useState('');
  const [expiryDate, setExpiryDate] = useState('');
  const [cvc, setCvc] = useState('');

  const {
    overlay,
    sheet,
    header,
    closeButton,
    logo,
    planTitle,
    planPriceText,
    renewalText,
    sectionTitle,
    emailInput,
    cardFieldContainer,
    cardNumberContainer,
    cardNumberInput,
    cardRowContainer,
    cardExpiryContainer,
    cardExpiryInput,
    cardCvcContainer,
    cardCvcInput,
    cvcIcon,
    cardIcons,
    cardholderInput,
    countryContainer,
    countryText,
    chevronDown,
    confirmButton,
    confirmButtonDisabled,
    confirmButtonText,
    confirmButtonTextDisabled,
  } = styles;

  // Card input handlers
  const handleCardNumberChange = (text: string) => {
    // Format card number with spaces (XXXX XXXX XXXX XXXX)
    const cleaned = text.replace(/\s/g, '');
    const formatted = cleaned.replace(/(.{4})/g, '$1 ').trim();
    setCardNumber(formatted);
  };

  const handleExpiryChange = (text: string) => {
    // Format expiry date (MM/YY)
    const cleaned = text.replace(/\D/g, '');
    if (cleaned.length >= 2) {
      const formatted = cleaned.substring(0, 2) + '/' + cleaned.substring(2, 4);
      setExpiryDate(formatted);
    } else {
      setExpiryDate(cleaned);
    }
  };

  const handleCvcChange = (text: string) => {
    // Only allow numbers
    const cleaned = text.replace(/\D/g, '');
    setCvc(cleaned);
  };

  const handlePayment = async () => {
    if (!cardDetails?.complete || !cardholderName.trim() || !email.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    setIsProcessing(true);
    try {
      // REAL IMPLEMENTATION (currently commented out):
      // const result = await paymentSheetServices.processPayment({
      //   planType,
      //   email,
      //   cardholderName,
      //   country,
      //   cardDetails, // This contains the card info from Stripe CardField
      //   confirmPayment, // Pass the Stripe confirmPayment function
      //   createPaymentMethod, // Pass the Stripe createPaymentMethod function
      // });

      // MOCK IMPLEMENTATION (remove when implementing real payment):
      const result = await paymentSheetServices.processPayment({
        planType,
        email,
        cardholderName,
        country,
        cardDetails,
      });

      if (result.success) {
        Alert.alert('Success', 'Payment processed successfully!', [
          { text: 'OK', onPress: onClose },
        ]);
      } else {
        Alert.alert('Error', result.error || 'Payment failed');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setIsProcessing(false);
    }
  };

  const isFormValid = cardNumber.trim().length >= 15 &&
                     expiryDate.length === 5 &&
                     cvc.length >= 3 &&
                     cardholderName.trim() &&
                     email.trim();

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent
      onRequestClose={onClose}
    >
      <View style={overlay}>
        <View style={sheet}>
          {/* Header */}
          <View style={header}>
            <TouchableOpacity style={closeButton} onPress={onClose}>
              <Close color={COLOURS.primary} />
            </TouchableOpacity>
          </View>

          <ScrollView showsVerticalScrollIndicator={false}>
            {/* Logo and Plan Info */}
            <View style={{ alignItems: 'center', marginBottom: 24 }}>
              <VetAssistIcon style={logo} />
              <TextTypes
                type="h4"
                color={COLOURS.secondaryTint}
                customStyle={planTitle}
              >
                {planType === 'ultimate' ? 'Ultimate Monthly Upgrade Plan' : 'Plus Monthly Upgrade Plan'}
              </TextTypes>
              <TextTypes
                type="h2"
                color={COLOURS.textBlack}
                customStyle={planPriceText}
              >
                {planPrice}
              </TextTypes>
              <TextTypes
                type="body2"
                color={COLOURS.secondaryTint}
                customStyle={renewalText}
              >
                Your plan renews automatically each month.{'\n'}
                Charges will be made to the card you provide{'\n'}
                today unless updated.
              </TextTypes>
            </View>

            {/* Contact Information */}
            <TextTypes
              type="h4"
              color={COLOURS.textBlack}
              customStyle={sectionTitle}
            >
              Contact information
            </TextTypes>
            <TextInput
              style={emailInput}
              placeholder="Email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />

            {/* Payment Method */}
            <TextTypes
              type="h4"
              color={COLOURS.textBlack}
              customStyle={sectionTitle}
            >
              Payment method
            </TextTypes>
            <TextTypes
              type="body2"
              color={COLOURS.secondaryTint}
              customStyle={{ marginBottom: 8 }}
            >
              Card information
            </TextTypes>
            
            {/* Card Number Field */}
            <View style={cardNumberContainer}>
              <TextInput
                style={cardNumberInput}
                placeholder="Enter text"
                placeholderTextColor={COLOURS.placeholderText}
                value={cardNumber}
                onChangeText={handleCardNumberChange}
                keyboardType="numeric"
                maxLength={19} // 16 digits + 3 spaces
              />
              <View style={cardIcons}>
                <View style={{ width: 24, height: 16, backgroundColor: '#EB001B', borderRadius: 2, marginRight: 4 }} />
                <View style={{ width: 24, height: 16, backgroundColor: '#1A1F71', borderRadius: 2, marginRight: 4 }} />
                <View style={{ width: 24, height: 16, backgroundColor: '#0070BA', borderRadius: 2, marginRight: 4 }} />
                <View style={{ width: 24, height: 16, backgroundColor: '#FF5F00', borderRadius: 2 }} />
              </View>
            </View>

            {/* MM/YY and CVC Row */}
            <View style={cardRowContainer}>
              <View style={cardExpiryContainer}>
                <TextInput
                  style={cardExpiryInput}
                  placeholder="MM / YY"
                  placeholderTextColor={COLOURS.placeholderText}
                  value={expiryDate}
                  onChangeText={handleExpiryChange}
                  keyboardType="numeric"
                  maxLength={5}
                />
              </View>
              <View style={cardCvcContainer}>
                <TextInput
                  style={cardCvcInput}
                  placeholder="CVC"
                  placeholderTextColor={COLOURS.placeholderText}
                  value={cvc}
                  onChangeText={handleCvcChange}
                  keyboardType="numeric"
                  maxLength={4}
                  secureTextEntry
                />
                <View style={cvcIcon}>
                  <TextTypes type="body3" color={COLOURS.placeholderText}>?</TextTypes>
                </View>
              </View>
            </View>

            {/* Cardholder Name */}
            <TextTypes
              type="body2"
              color={COLOURS.secondaryTint}
              customStyle={{ marginBottom: 8, marginTop: 16 }}
            >
              Cardholder name
            </TextTypes>
            <TextInput
              style={cardholderInput}
              placeholder="Full name on card"
              value={cardholderName}
              onChangeText={setCardholderName}
            />

            {/* Country */}
            <TextTypes
              type="body2"
              color={COLOURS.secondaryTint}
              customStyle={{ marginBottom: 8, marginTop: 16 }}
            >
              Country or region
            </TextTypes>
            <TouchableOpacity style={countryContainer}>
              <TextTypes type="body1" color={COLOURS.textBlack} customStyle={countryText}>
                {country}
              </TextTypes>
              <View style={chevronDown} />
            </TouchableOpacity>

            {/* Confirm Button */}
            <TouchableOpacity
              style={[
                confirmButton,
                (!isFormValid || isProcessing) && confirmButtonDisabled,
              ]}
              onPress={handlePayment}
              disabled={!isFormValid || isProcessing}
            >
              <TextTypes
                type="h4"
                color={COLOURS.white}
                customStyle={[
                  confirmButtonText,
                  (!isFormValid || isProcessing) && confirmButtonTextDisabled,
                ]}
              >
                {isProcessing ? 'PROCESSING...' : 'CONFIRM UPGRADE'}
              </TextTypes>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

export default PaymentSheet;
