import React, { useState } from 'react';
import {
  View,
  Modal,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
} from 'react-native';
import { CardField, useStripe } from '@stripe/stripe-react-native';

import TextTypes from '@/components/text-types';
import { Close, VetAssistIcon } from '@/components/icons';
import { COLOURS } from '@/constants/colours';
import { paymentSheetServices } from './services';
import styles from './styles';

interface PaymentSheetProps {
  visible: boolean;
  onClose: () => void;
  planType: 'plus' | 'ultimate';
  planPrice: string;
  userEmail?: string;
}

const PaymentSheet: React.FC<PaymentSheetProps> = ({
  visible,
  onClose,
  planType,
  planPrice,
  userEmail = '',
}) => {
  const { confirmPayment, createPaymentMethod } = useStripe();
  const [email, setEmail] = useState(userEmail);
  const [cardholderName, setCardholderName] = useState('');
  const [country, setCountry] = useState('United Kingdom');
  const [isProcessing, setIsProcessing] = useState(false);
  const [cardDetails, setCardDetails] = useState<any>(null);

  const {
    overlay,
    sheet,
    header,
    closeButton,
    logo,
    planTitle,
    planPriceText,
    renewalText,
    sectionTitle,
    emailInput,
    cardFieldContainer,
    cardIcons,
    cardholderInput,
    countryContainer,
    countryText,
    chevronDown,
    confirmButton,
    confirmButtonDisabled,
    confirmButtonText,
    confirmButtonTextDisabled,
  } = styles;

  const handlePayment = async () => {
    if (!cardDetails?.complete || !cardholderName.trim() || !email.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    setIsProcessing(true);
    try {
      // REAL IMPLEMENTATION (currently commented out):
      // const result = await paymentSheetServices.processPayment({
      //   planType,
      //   email,
      //   cardholderName,
      //   country,
      //   cardDetails, // This contains the card info from Stripe CardField
      //   confirmPayment, // Pass the Stripe confirmPayment function
      //   createPaymentMethod, // Pass the Stripe createPaymentMethod function
      // });

      // MOCK IMPLEMENTATION (remove when implementing real payment):
      const result = await paymentSheetServices.processPayment({
        planType,
        email,
        cardholderName,
        country,
        cardDetails,
      });

      if (result.success) {
        Alert.alert('Success', 'Payment processed successfully!', [
          { text: 'OK', onPress: onClose },
        ]);
      } else {
        Alert.alert('Error', result.error || 'Payment failed');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setIsProcessing(false);
    }
  };

  const isFormValid = cardDetails?.complete && cardholderName.trim() && email.trim();

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent
      onRequestClose={onClose}
    >
      <View style={overlay}>
        <View style={sheet}>
          {/* Header */}
          <View style={header}>
            <TouchableOpacity style={closeButton} onPress={onClose}>
              <Close color={COLOURS.primary} />
            </TouchableOpacity>
          </View>

          <ScrollView showsVerticalScrollIndicator={false}>
            {/* Logo and Plan Info */}
            <View style={{ alignItems: 'center', marginBottom: 24 }}>
              <VetAssistIcon style={logo} />
              <TextTypes
                type="h4"
                color={COLOURS.secondaryTint}
                customStyle={planTitle}
              >
                {planType === 'ultimate' ? 'Ultimate Monthly Upgrade Plan' : 'Plus Monthly Upgrade Plan'}
              </TextTypes>
              <TextTypes
                type="h2"
                color={COLOURS.textBlack}
                customStyle={planPriceText}
              >
                {planPrice}
              </TextTypes>
              <TextTypes
                type="body2"
                color={COLOURS.secondaryTint}
                customStyle={renewalText}
              >
                Your plan renews automatically each month.{'\n'}
                Charges will be made to the card you provide{'\n'}
                today unless updated.
              </TextTypes>
            </View>

            {/* Contact Information */}
            <TextTypes
              type="h4"
              color={COLOURS.textBlack}
              customStyle={sectionTitle}
            >
              Contact information
            </TextTypes>
            <TextInput
              style={emailInput}
              placeholder="Email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />

            {/* Payment Method */}
            <TextTypes
              type="h4"
              color={COLOURS.textBlack}
              customStyle={sectionTitle}
            >
              Payment method
            </TextTypes>
            <TextTypes
              type="body2"
              color={COLOURS.secondaryTint}
              customStyle={{ marginBottom: 8 }}
            >
              Card information
            </TextTypes>
            
            <View style={cardFieldContainer}>
              <CardField
                postalCodeEnabled={false}
                placeholders={{
                  number: 'Enter text',
                }}
                cardStyle={{
                  backgroundColor: COLOURS.white,
                  textColor: COLOURS.textBlack,
                  fontSize: 16,
                  placeholderColor: COLOURS.placeholderText,
                  borderWidth: 0,
                }}
                style={{
                  flex: 1,
                  height: 50,
                  marginLeft: 16,
                }}
                onCardChange={(details) => {
                  setCardDetails(details);
                }}
              />
              <View style={cardIcons}>
                <View style={{ width: 24, height: 16, backgroundColor: '#EB001B', borderRadius: 2, marginRight: 4 }} />
                <View style={{ width: 24, height: 16, backgroundColor: '#1A1F71', borderRadius: 2, marginRight: 4 }} />
                <View style={{ width: 24, height: 16, backgroundColor: '#0070BA', borderRadius: 2, marginRight: 4 }} />
                <View style={{ width: 24, height: 16, backgroundColor: '#FF5F00', borderRadius: 2 }} />
              </View>
            </View>

            {/* Cardholder Name */}
            <TextTypes
              type="body2"
              color={COLOURS.secondaryTint}
              customStyle={{ marginBottom: 8, marginTop: 16 }}
            >
              Cardholder name
            </TextTypes>
            <TextInput
              style={cardholderInput}
              placeholder="Full name on card"
              value={cardholderName}
              onChangeText={setCardholderName}
            />

            {/* Country */}
            <TextTypes
              type="body2"
              color={COLOURS.secondaryTint}
              customStyle={{ marginBottom: 8, marginTop: 16 }}
            >
              Country or region
            </TextTypes>
            <TouchableOpacity style={countryContainer}>
              <TextTypes type="body1" color={COLOURS.textBlack} customStyle={countryText}>
                {country}
              </TextTypes>
              <View style={chevronDown} />
            </TouchableOpacity>

            {/* Confirm Button */}
            <TouchableOpacity
              style={[
                confirmButton,
                (!isFormValid || isProcessing) && confirmButtonDisabled,
              ]}
              onPress={handlePayment}
              disabled={!isFormValid || isProcessing}
            >
              <TextTypes
                type="h4"
                color={COLOURS.white}
                customStyle={[
                  confirmButtonText,
                  (!isFormValid || isProcessing) && confirmButtonTextDisabled,
                ]}
              >
                {isProcessing ? 'PROCESSING...' : 'CONFIRM UPGRADE'}
              </TextTypes>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

export default PaymentSheet;
