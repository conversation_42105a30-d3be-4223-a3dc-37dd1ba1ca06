# Payment Sheet Implementation Guide

## Overview

This guide shows how to implement the real Stripe payment processing by uncommenting and configuring the provided code.

## Current Status

✅ **Mock Implementation Active** - The payment sheet currently uses mock payment processing  
🔧 **Real Implementation Ready** - All real Stripe code is commented and ready to use

## Steps to Enable Real Stripe Processing

### 1. Backend Setup

First, implement the required backend endpoints as documented in `BACKEND_API.md`:

- `POST /api/v1/payments/create-intent`
- `POST /api/v1/subscriptions/activate`
- `POST /api/v1/webhooks/stripe` (recommended)

### 2. Environment Configuration

Add your Stripe keys to your environment:

```env
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_real_key_here
```

### 3. Code Changes Required

#### A. Update `services.ts`

1. **Import required functions:**
```typescript
import { getAccessToken } from '@/lib/vet247/auth'; // Your auth helper
```

2. **Uncomment the real implementation in `processPayment`:**
   - Remove the "MOCK IMPLEMENTATION" section (lines ~96-119)
   - Uncomment the "REAL IMPLEMENTATION" section (lines ~21-95)

3. **Update the PaymentData interface:**
```typescript
interface PaymentData {
  planType: 'plus' | 'ultimate';
  email: string;
  cardholderName: string;
  country: string;
  cardDetails: any;
  confirmPayment: (clientSecret: string, data: any) => Promise<any>; // Uncomment this
}
```

#### B. Update `index.tsx`

1. **Uncomment the real implementation in `handlePayment`:**
   - Remove the "MOCK IMPLEMENTATION" section (lines ~78-85)
   - Uncomment the "REAL IMPLEMENTATION" section (lines ~68-77)

2. **Pass confirmPayment to the service:**
```typescript
const result = await paymentSheetServices.processPayment({
  planType,
  email,
  cardholderName,
  country,
  cardDetails,
  confirmPayment, // Add this line
});
```

### 4. Testing

#### Test Cards (Stripe Test Mode)
- **Success:** ****************
- **Decline:** ****************
- **Authentication Required:** ****************

#### Test Flow
1. Select a plan on upgrade screen
2. Click "CONTINUE TO PAYMENT"
3. Fill in test card details
4. Verify payment processes correctly
5. Check backend logs for payment intent creation
6. Verify subscription activation

### 5. Production Deployment

1. **Replace test keys with live keys:**
```env
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_live_key_here
```

2. **Configure webhooks in Stripe Dashboard:**
   - Add your webhook endpoint URL
   - Select events: `payment_intent.succeeded`, `payment_intent.payment_failed`
   - Copy webhook signing secret to your backend

3. **Test with real cards** in a staging environment first

## Code Structure

```
components/ui/payment-sheet/
├── index.tsx           # Main component with UI
├── services.ts         # Payment processing logic
├── styles.ts          # Component styling
├── README.md          # Component documentation
├── BACKEND_API.md     # Backend API requirements
└── IMPLEMENTATION_GUIDE.md # This file
```

## Key Features Implemented

✅ **Stripe CardField Integration** - Secure card input  
✅ **Form Validation** - Email, name, and card validation  
✅ **Loading States** - Processing indicators  
✅ **Error Handling** - User-friendly error messages  
✅ **Payment Intent Creation** - Backend API integration ready  
✅ **Payment Confirmation** - Stripe payment processing  
✅ **Subscription Activation** - Post-payment user upgrade  

## Security Notes

- ✅ Card details never stored locally
- ✅ Stripe handles all sensitive card data
- ✅ Payment confirmation happens on backend
- ✅ Webhook verification for payment status
- ✅ Proper authentication for all API calls

## Troubleshooting

### Common Issues

1. **"Payment failed" errors:**
   - Check Stripe publishable key is correct
   - Verify backend API endpoints are working
   - Check network connectivity

2. **Card field not working:**
   - Ensure Stripe plugin is properly configured in app.json
   - Check iOS/Android build includes Stripe native modules

3. **Payment intent creation fails:**
   - Verify backend API authentication
   - Check Stripe secret key on backend
   - Review backend logs for errors

### Debug Mode

Enable debug logging by adding to your services:

```typescript
console.log('Payment data:', paymentData);
console.log('Payment intent response:', response);
console.log('Stripe confirmation result:', result);
```

## Support

For Stripe-specific issues, refer to:
- [Stripe React Native Documentation](https://stripe.com/docs/payments/accept-a-payment?platform=react-native)
- [Stripe API Reference](https://stripe.com/docs/api)
- [Stripe Testing Guide](https://stripe.com/docs/testing)
