interface CardDetails {
  number: string;
  expiryMonth: string;
  expiryYear: string;
  cvc: string;
  complete: boolean;
}

interface PaymentData {
  planType: 'plus' | 'ultimate';
  email: string;
  cardholderName: string;
  country: string;
  cardDetails: CardDetails;
  // For real implementation, you need to pass these Stripe functions:
  // confirmPayment?: (clientSecret: string, data: any) => Promise<any>;
  // createPaymentMethod?: (data: any) => Promise<any>;
}

interface PaymentResult {
  success: boolean;
  error?: string;
  paymentIntentId?: string;
}

export const paymentSheetServices = {
  // Process payment with Stripe
  processPayment: async (paymentData: PaymentData): Promise<PaymentResult> => {
    try {
      // REAL IMPLEMENTATION (currently commented out):

      // Step 1: Create a payment intent on your backend
      // const response = await fetch(`${process.env.EXPO_PUBLIC_EQUIVET_URL}/api/v1/payments/create-intent`, {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //     'Authorization': `Bearer ${await getAccessToken()}`, // Your auth token
      //   },
      //   body: JSON.stringify({
      //     amount: paymentSheetServices.getPlanAmount(paymentData.planType),
      //     currency: 'gbp',
      //     customer_email: paymentData.email,
      //     plan_type: paymentData.planType,
      //     metadata: {
      //       cardholder_name: paymentData.cardholderName,
      //       country: paymentData.country,
      //     },
      //   }),
      // });

      // if (!response.ok) {
      //   throw new Error('Failed to create payment intent');
      // }

      // const { client_secret, payment_intent_id } = await response.json();

      // Step 2: Confirm the payment with Stripe
      // const { createPaymentMethod } = useStripe(); // This would also need to be passed
      // const { error: pmError, paymentMethod } = await createPaymentMethod({
      //   paymentMethodType: 'Card',
      //   card: paymentData.cardDetails, // This is where we use the card details from CardField
      //   billingDetails: {
      //     name: paymentData.cardholderName,
      //     email: paymentData.email,
      //   },
      // });

      // if (pmError) {
      //   return { success: false, error: pmError.message };
      // }

      // // Then confirm with the payment method
      // const { error, paymentIntent } = await confirmPayment(client_secret, {
      //   paymentMethodId: paymentMethod.id,
      // });

      // Step 3: Handle the result
      // if (error) {
      //   console.error('Payment confirmation error:', error);
      //   return {
      //     success: false,
      //     error: error.message || 'Payment failed. Please try again.',
      //   };
      // }

      // if (paymentIntent?.status === 'succeeded') {
      //   // Step 4: Update user subscription on backend
      //   await fetch(`${process.env.EXPO_PUBLIC_EQUIVET_URL}/api/v1/subscriptions/activate`, {
      //     method: 'POST',
      //     headers: {
      //       'Content-Type': 'application/json',
      //       'Authorization': `Bearer ${await getAccessToken()}`,
      //     },
      //     body: JSON.stringify({
      //       payment_intent_id: paymentIntent.id,
      //       plan_type: paymentData.planType,
      //     }),
      //   });

      //   return {
      //     success: true,
      //     paymentIntentId: paymentIntent.id,
      //   };
      // } else {
      //   return {
      //     success: false,
      //     error: 'Payment was not completed successfully.',
      //   };
      // }

      // MOCK IMPLEMENTATION (remove when implementing real payment):
      console.log('Processing payment for:', {
        planType: paymentData.planType,
        email: paymentData.email,
        cardholderName: paymentData.cardholderName,
        country: paymentData.country,
        cardNumber: `****-****-****-${paymentData.cardDetails.number.slice(-4)}`,
        expiryDate: `${paymentData.cardDetails.expiryMonth}/${paymentData.cardDetails.expiryYear}`,
        cardComplete: paymentData.cardDetails.complete,
      });

      // Mock payment processing for now
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simulate success/failure based on card completion
      const isSuccess = paymentData.cardDetails.complete && Math.random() > 0.1; // 90% success rate for demo

      if (isSuccess) {
        return {
          success: true,
          paymentIntentId: 'pi_mock_' + Date.now(),
        };
      } else {
        return {
          success: false,
          error: 'Payment failed. Please check your card details and try again.',
        };
      }
    } catch (error) {
      console.error('Payment processing error:', error);
      return {
        success: false,
        error: 'An unexpected error occurred during payment processing.',
      };
    }
  },

  // Create payment intent on backend (to be implemented)
  createPaymentIntent: async (amount: number, currency: string = 'gbp') => {
    // REAL IMPLEMENTATION (currently commented out):

    // const response = await fetch(`${process.env.EXPO_PUBLIC_EQUIVET_URL}/api/v1/payments/create-intent`, {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //     'Authorization': `Bearer ${await getAccessToken()}`,
    //   },
    //   body: JSON.stringify({
    //     amount, // Amount in pence (e.g., 900 for £9.00)
    //     currency, // 'gbp'
    //     automatic_payment_methods: {
    //       enabled: true,
    //     },
    //   }),
    // });

    // if (!response.ok) {
    //   throw new Error('Failed to create payment intent');
    // }

    // const data = await response.json();
    // return {
    //   clientSecret: data.client_secret,
    //   paymentIntentId: data.id,
    // };

    // MOCK IMPLEMENTATION:
    console.log('Creating payment intent for amount:', amount, currency);
    return {
      clientSecret: 'pi_mock_client_secret_' + Date.now(),
    };
  },

  // Get plan amount based on plan type
  getPlanAmount: (planType: 'plus' | 'ultimate'): number => {
    switch (planType) {
      case 'plus':
        return 600; // £6.00 in pence
      case 'ultimate':
        return 900; // £9.00 in pence
      default:
        return 0;
    }
  },

  // Format price for display
  formatPrice: (planType: 'plus' | 'ultimate'): string => {
    switch (planType) {
      case 'plus':
        return '£6.00 /month';
      case 'ultimate':
        return '£9.00 /month';
      default:
        return '£0.00 /month';
    }
  },

  // Validate email format
  validateEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // Validate cardholder name
  validateCardholderName: (name: string): boolean => {
    return name.trim().length >= 2;
  },
};
