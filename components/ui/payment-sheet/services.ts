interface PaymentData {
  planType: 'plus' | 'ultimate';
  email: string;
  cardholderName: string;
  country: string;
  cardDetails: any;
}

interface PaymentResult {
  success: boolean;
  error?: string;
  paymentIntentId?: string;
}

export const paymentSheetServices = {
  // Process payment with Stripe
  processPayment: async (paymentData: PaymentData): Promise<PaymentResult> => {
    try {
      // In a real implementation, you would:
      // 1. Create a payment intent on your backend
      // 2. Confirm the payment with Stripe
      // 3. Handle the result
      
      console.log('Processing payment for:', paymentData);
      
      // Mock payment processing for now
      // Replace this with actual Stripe integration
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate success/failure
      const isSuccess = Math.random() > 0.1; // 90% success rate for demo
      
      if (isSuccess) {
        return {
          success: true,
          paymentIntentId: 'pi_mock_' + Date.now(),
        };
      } else {
        return {
          success: false,
          error: 'Payment failed. Please check your card details and try again.',
        };
      }
    } catch (error) {
      console.error('Payment processing error:', error);
      return {
        success: false,
        error: 'An unexpected error occurred during payment processing.',
      };
    }
  },

  // Create payment intent on backend (to be implemented)
  createPaymentIntent: async (amount: number, currency: string = 'gbp') => {
    // This would call your backend API to create a payment intent
    // Example:
    // const response = await fetch('/api/create-payment-intent', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ amount, currency }),
    // });
    // return response.json();
    
    console.log('Creating payment intent for amount:', amount, currency);
    return {
      clientSecret: 'pi_mock_client_secret_' + Date.now(),
    };
  },

  // Get plan amount based on plan type
  getPlanAmount: (planType: 'plus' | 'ultimate'): number => {
    switch (planType) {
      case 'plus':
        return 600; // £6.00 in pence
      case 'ultimate':
        return 900; // £9.00 in pence
      default:
        return 0;
    }
  },

  // Format price for display
  formatPrice: (planType: 'plus' | 'ultimate'): string => {
    switch (planType) {
      case 'plus':
        return '£6.00 /month';
      case 'ultimate':
        return '£9.00 /month';
      default:
        return '£0.00 /month';
    }
  },

  // Validate email format
  validateEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // Validate cardholder name
  validateCardholderName: (name: string): boolean => {
    return name.trim().length >= 2;
  },
};
