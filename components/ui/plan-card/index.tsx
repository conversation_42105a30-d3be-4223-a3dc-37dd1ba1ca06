import React from 'react';
import { TouchableOpacity, View } from 'react-native';

import { Badge, CheckCircle, CupIcon } from '@/components/icons';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';

import styles from './styles';
import { PlanCardProps } from './services';


const {
  planCard,
  planCardSelected,
  planIcon,
  planName,
  planPrice,
  featureList,
  featureItem,
  featureText,
} = styles;

const PlanCard: React.FC<PlanCardProps> = ({ plan, isSelected, onSelect }) => {
  const renderIcon = () => {
    const iconProps = {
      width: 32,
      height: 32,
      color: isSelected ? COLOURS.white : COLOURS.secondaryShade,
    };

    switch (plan.icon) {
      case 'badge':
        return <Badge {...iconProps} />;
      case 'cup':
        return <CupIcon {...iconProps} />;
      default:
        return <Badge {...iconProps} />;
    }
  };

  return (
    <TouchableOpacity
      style={[planCard, isSelected && planCardSelected]}
      onPress={() => onSelect(plan.id)}
    >
      <View style={planIcon}>
        {renderIcon()}
      </View>
      
      <TextTypes
        type='h2'
        color={isSelected ? COLOURS.white : COLOURS.secondaryShade}
        customStyle={planName}
      >
        {plan.name}
      </TextTypes>
      
      <TextTypes
        type='h4'
        color={isSelected ? COLOURS.white : COLOURS.secondaryShade}
        customStyle={planPrice}
      >
        {plan.price}
      </TextTypes>
      
      <View style={featureList}>
        {plan.features.map((feature) => (
          <View key={feature.id} style={featureItem}>
            <CheckCircle
              width={20}
              height={20}
              color={COLOURS.tertiaryShade}
            />
            <TextTypes
              type='body3'
              color={isSelected ? COLOURS.white : COLOURS.textBlack}
              customStyle={featureText}
            >
              {feature.text}
            </TextTypes>
          </View>
        ))}
      </View>
    </TouchableOpacity>
  );
};

export default PlanCard;
