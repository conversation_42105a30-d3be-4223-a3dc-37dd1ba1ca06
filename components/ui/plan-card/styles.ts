import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  planCard: {
    backgroundColor: COLOURS.white,
    borderRadius: 16,
    padding: 24,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: COLOURS.borderColor,
    shadowColor: COLOURS.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  planCardSelected: {
    backgroundColor: COLOURS.secondaryShade,
    borderColor: COLOURS.secondaryShade,
  },
  planIcon: {
    width: 52,
    height: 52,
    borderRadius: 30,
    backgroundColor: COLOURS.upgradePurple40,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 10,
  },
  planName: {
    textAlign: 'center',
    marginBottom: 8,
  },
  planPrice: {
    textAlign: 'center',
    marginBottom: 16,
  },
  featureList: {
    gap: 12,
  },
  featureItem: {
    flexDirection: 'row',
  },
  featureText: {
    flex: 1,
    marginLeft: 9,
    lineHeight: 21,
  },
});

export default styles;
