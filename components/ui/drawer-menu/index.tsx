import { DrawerContentComponentProps } from '@react-navigation/drawer';
import { useRouter } from 'expo-router';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { VetAssistIcon } from '@/components/icons';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';

import { DrawerMenuItem, menuItems } from './services';
import styles from './styles';

const {
  drawerContainer,
  logoContainer,
  menuList,
  menuItem,
  icon,
  menuTitle,
  badge,
} = styles;

const DrawerMenu: React.FC<DrawerContentComponentProps> = () => {
  const insects = useSafeAreaInsets();
  const router = useRouter();
  const { t } = useTranslation();

  const handleMenuPress = (key: string) => {
    if (key === 'new-chat') {
      router.push({ pathname: '/(tabs)', params: { isNewChat: '1' } });
    } else if (key === 'settings') {
      router.push('/(tabs)/settings');
    }
  };

  return (
    <View style={drawerContainer}>
      <View style={[logoContainer, { marginTop: insects.bottom + 30 }]}>
        <VetAssistIcon />
      </View>
      <View style={menuList}>
        {menuItems.map(
          ({ key, icon: Icon, label, badgeCount }: DrawerMenuItem) => (
            <TouchableOpacity
              key={key}
              style={menuItem}
              onPress={() => handleMenuPress(key)}
            >
              <Icon style={icon} />
              <TextTypes
                customStyle={menuTitle}
                type='h5'
              >
                {t(label)}
              </TextTypes>
              {badgeCount !== undefined && (
                <View style={badge}>
                  <TextTypes color={COLOURS.white} type={'errorText'}>
                    {badgeCount}
                  </TextTypes>
                </View>
              )}
            </TouchableOpacity>
          )
        )}
      </View>
    </View>
  );
};

export default DrawerMenu;
