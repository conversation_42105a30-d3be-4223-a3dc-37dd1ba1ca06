{"expo": {"name": "VetAssist", "slug": "vetassist", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/appIcon.png", "backgroundColor": "#256B74", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/appIcon.png", "resizeMode": "contain", "backgroundColor": "#256B74"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.vetassist.app"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/appIcon.png", "backgroundColor": "#ffffff"}, "package": "com.vetassist.app"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/appIcon.png", "imageWidth": 300, "resizeMode": "contain", "backgroundColor": "#256B74", "androidSplashResourceName": "splash_screen", "androidScaleType": "CENTER_CROP", "androidSplashResourceBackgroundColor": "#256B74", "iosSplashResourceName": "SplashScreen", "iosSplashResourceBackgroundColor": "#256B74"}], "expo-sqlite", ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": true}], ["expo-image-picker", {"photosPermission": "This app requires photos access to allow users to capture photos for vet assistance."}], ["@stripe/stripe-react-native", {"merchantIdentifier": "merchant.com.vetassist.app", "enableGooglePay": true}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "cb77376b-6b41-44a8-8594-3a5d9315cced"}}, "owner": "scottpet247"}}